import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Card,
  Typography,
  Tabs,
  Button,
  Space,
  Badge,
  Tag,
  Row,
  Col,
  List,
  Avatar,
  Tooltip,
  Empty,
  message,
  Descriptions,
  Statistic,
  Result,
  Table,
  Spin,
  Dropdown,
  Menu
} from 'antd';
import {
  LeftOutlined,
  MessageOutlined,
  EnvironmentOutlined,
  ApartmentOutlined,
  TeamOutlined,

  StopOutlined,
  EyeOutlined,
  ExportOutlined,
  GlobalOutlined,
  RobotOutlined,
  ToolOutlined,
  ReloadOutlined,
  DownOutlined,
  SettingOutlined,
  ImportOutlined,
  BookOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  InfoCircleOutlined,
  BranchesOutlined
} from '@ant-design/icons';
import { actionTaskAPI } from '../../services/api/actionTask';
import conversationAPI from '../../services/api/conversation';
// 导入对话样式
import './css/conversation.css';
import ActionTaskConversation from './components/ActionTaskConversation';
import ActionTaskEnvironment from './components/ActionTaskEnvironment';
import ActionTaskRules from './components/ActionTaskRules';
import ActionTaskSupervisor from './components/ActionTaskSupervisor';
import ActionTaskWorkspace from './components/ActionTaskWorkspace';
import AutonomousTaskCard from './components/AutonomousTaskCard';
// 导入智能体颜色工具函数
import { getAgentAvatarStyle } from '../../utils/colorUtils';

const { Title, Text } = Typography;

// 变量闪烁效果的CSS样式
const variableFlashStyle = `
  @keyframes variableFlash {
    0% { background-color: rgba(24, 144, 255, 0.2); }
    50% { background-color: rgba(24, 144, 255, 0.5); }
    100% { background-color: rgba(24, 144, 255, 0.2); }
  }

  .variable-flash {
    animation: variableFlash 1s ease-in-out;
    border-radius: 2px;
  }
`;

const ActionTaskDetail = () => {
  const { taskId } = useParams();
  const navigate = useNavigate();
  const [task, setTask] = useState(null);
  const [messages, setMessages] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshKey, setRefreshKey] = useState(0);
  const [variablesRefreshKey, setVariablesRefreshKey] = useState(0);
  const [respondingAgentId, setRespondingAgentId] = useState(null);
  const [previousVariables, setPreviousVariables] = useState({
    environment: [],
    agent: {}
  });
  const [sidebarVisible, setSidebarVisible] = useState(true);
  const [activeSidebarTab, setActiveSidebarTab] = useState('info');
  const [leftColSpan, setLeftColSpan] = useState(16);
  const [rightColSpan, setRightColSpan] = useState(8);
  const [isDragging, setIsDragging] = useState(false);
  const dragHandleRef = useRef(null);
  const [activeConversationId, setActiveConversationId] = useState(null);

  // 组件引用
  const conversationRef = useRef(null);

  // 刷新环境变量和智能体变量的函数
  const refreshVariables = async () => {
    if (!task || !task.id) return;

    try {
      console.log('正在刷新环境变量和智能体变量...');

      // 保存当前变量状态用于比较
      const currentEnvVars = task.environment_variables || [];
      const currentAgentVars = task.agent_variables || [];

      // 构建当前变量的映射，用于后续比较
      const currentEnvVarsMap = {};
      currentEnvVars.forEach(v => {
        currentEnvVarsMap[v.name] = v.value;
      });

      // 构建当前智能体变量的映射
      const currentAgentVarsMap = {};
      currentAgentVars.forEach(v => {
        if (!currentAgentVarsMap[v.agent_id]) {
          currentAgentVarsMap[v.agent_id] = {};
        }
        currentAgentVarsMap[v.agent_id][v.name] = v.value;
      });

      // 使用批量API一次获取所有变量
      const batchVariables = await actionTaskAPI.getBatchVariables(task.id);
      console.log('获取到批量变量数据:', batchVariables);

      // 标记变化的环境变量，包括新创建的变量
      const markedEnvVars = batchVariables.environmentVariables.map(v => {
        // 检查是否是新变量（当前映射中不存在）
        const isNewVar = currentEnvVarsMap[v.name] === undefined;
        // 检查值是否变化
        const valueChanged = !isNewVar && String(currentEnvVarsMap[v.name]) !== String(v.value);
        // 新变量或值变化的变量都标记为已变化
        return {
          ...v,
          _hasChanged: isNewVar || valueChanged,
          _isNew: isNewVar // 额外标记是否为新变量，便于调试
        };
      });

      // 标记变化的智能体变量，包括新创建的变量
      const markedAgentVars = batchVariables.agentVariables.map(v => {
        const agentVars = currentAgentVarsMap[v.agent_id] || {};
        // 检查是否是新变量（当前映射中不存在）
        const isNewVar = agentVars[v.name] === undefined;
        // 检查值是否变化
        const valueChanged = !isNewVar && String(agentVars[v.name]) !== String(v.value);
        // 新变量或值变化的变量都标记为已变化
        return {
          ...v,
          _hasChanged: isNewVar || valueChanged,
          _isNew: isNewVar // 额外标记是否为新变量，便于调试
        };
      });

      // 打印新变量和变化的变量，便于调试
      const newEnvVars = markedEnvVars.filter(v => v._isNew);
      const changedEnvVars = markedEnvVars.filter(v => v._hasChanged && !v._isNew);

      if (newEnvVars.length > 0) {
        console.log('检测到新的环境变量:', newEnvVars.map(v => v.name));
      }

      if (changedEnvVars.length > 0) {
        console.log('检测到变化的环境变量:', changedEnvVars.map(v => v.name));
      }

      const newAgentVars = markedAgentVars.filter(v => v._isNew);
      const changedAgentVars = markedAgentVars.filter(v => v._hasChanged && !v._isNew);

      if (newAgentVars.length > 0) {
        console.log('检测到新的智能体变量:', newAgentVars.map(v => `${v.agent_id}:${v.name}`));
      }

      if (changedAgentVars.length > 0) {
        console.log('检测到变化的智能体变量:', changedAgentVars.map(v => `${v.agent_id}:${v.name}`));
      }

      // 检查是否有变化的变量
      const hasChanges = newEnvVars.length > 0 || changedEnvVars.length > 0 ||
                         newAgentVars.length > 0 || changedAgentVars.length > 0;

      // 更新任务状态，保留其他字段不变
      setTask(prevTask => {
        const updatedTask = {
          ...prevTask,
          environment_variables: markedEnvVars,
          agent_variables: markedAgentVars
        };
        console.log('更新后的任务状态:', updatedTask);
        return updatedTask;
      });

      // 刷新变量表格部分
      setVariablesRefreshKey(prev => prev + 1);

      // 只刷新自主任务卡片，不刷新整个对话组件
      if (hasChanges) {
        setRefreshKey(prev => prev + 1);
        console.log('检测到变量变化，已更新自主任务卡片刷新键');
      } else {
        console.log('未检测到变量变化，跳过组件刷新');
      }

      if (hasChanges) {
        console.log('检测到变量变化，已更新UI');
      } else {
        console.log('未检测到变量变化，但已刷新所有组件');
      }

      // 保存当前变量状态用于下次比较
      setPreviousVariables({
        environment: markedEnvVars,
        agent: currentAgentVarsMap
      });

      console.log('变量刷新完成，最后更新时间:', batchVariables.lastUpdated);
      return hasChanges; // 返回是否有变化
    } catch (error) {
      console.error('刷新变量失败:', error);
      return false;
    }
  };

  // 刷新组件的函数
  const refreshComponent = () => {
    // Increment refresh key to force child components to re-render
    setRefreshKey(prev => prev + 1);
  };

  // 处理侧边栏标签页切换
  const handleSidebarTabChange = (key) => {
    setActiveSidebarTab(key);
  };

  // 切换侧边栏显示/隐藏
  const toggleSidebar = () => {
    setSidebarVisible(!sidebarVisible);
  };

  // 处理拖拽开始
  const handleDragStart = (e) => {
    e.preventDefault(); // 防止拖拽时选中文本

    const startX = e.clientX;
    const startLeftColSpan = leftColSpan;

    // 获取容器宽度
    const container = document.querySelector('.action-task-detail-page .ant-card .ant-row');
    if (!container) return;

    const containerWidth = container.offsetWidth;

    // 处理拖拽移动
    const handleDragMove = (moveEvent) => {
      // 计算鼠标移动的距离
      const deltaX = moveEvent.clientX - startX;

      // 计算移动距离对应的span变化
      const deltaSpan = Math.round((deltaX / containerWidth) * 24);

      // 计算新的span值（限制在5-19之间，确保两侧都有足够空间）
      const newLeftSpan = Math.min(Math.max(startLeftColSpan + deltaSpan, 5), 19);
      const newRightSpan = 24 - newLeftSpan;

      // 更新状态
      setLeftColSpan(newLeftSpan);
      setRightColSpan(newRightSpan);
      setIsDragging(true);
    };

    // 处理拖拽结束
    const handleDragEnd = () => {
      document.removeEventListener('mousemove', handleDragMove);
      document.removeEventListener('mouseup', handleDragEnd);
      setIsDragging(false);
    };

    // 添加事件监听器
    document.addEventListener('mousemove', handleDragMove);
    document.addEventListener('mouseup', handleDragEnd);
  };

  // 添加自定义样式
  const customStyles = `
    /* 拖拽分隔线样式 */
    .drag-handle {
      position: absolute;
      top: 0;
      bottom: 0;
      width: 8px;
      background-color: transparent;
      cursor: col-resize;
      z-index: 100;
      transition: background-color 0.2s;
    }
    .drag-handle:hover {
      background-color: rgba(24, 144, 255, 0.3);
    }
    .drag-handle.dragging {
      background-color: rgba(24, 144, 255, 0.5);
    }
    .drag-handle::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 2px;
      height: 50px;
      background-color: #d9d9d9;
    }
    .drag-handle:hover::after {
      background-color: #1890ff;
    }
    .drag-handle.dragging::after {
      background-color: #1890ff;
      width: 3px;
    }

    /* 拖拽时禁止选择文本 */
    .dragging-active {
      user-select: none;
    }

    /* 拖拽时添加过渡效果 */
    .resize-transition {
      transition: width 0.1s ease;
    }
    .no-transition {
      transition: none !important;
    }
    .full-height-tabs > .ant-tabs-content-holder {
      flex: 1;
      overflow: hidden;
      position: relative;
    }
    .full-height-tabs > .ant-tabs-content-holder > .ant-tabs-content {
      height: 100%;
      position: relative;
    }
    .full-height-tabs > .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
      height: 100%;
      overflow: hidden;
      position: relative;
    }
    .tab-content-container {
      position: relative;
      height: 100%;
      width: 100%;
      overflow: auto;
    }
    .message-history {
      position: relative !important;
      overflow-y: auto !important;
    }
    .message-input-area {
      position: relative !important;
      z-index: 1 !important;
      border-top: 1px solid #f0f0f0;
    }
  `;

  // 注入自定义样式
  useEffect(() => {
    // 创建style元素
    const styleEl = document.createElement('style');
    styleEl.setAttribute('id', 'action-task-detail-styles');
    styleEl.innerHTML = customStyles;
    document.head.appendChild(styleEl);

    // 清理函数
    return () => {
      const existingStyle = document.getElementById('action-task-detail-styles');
      if (existingStyle) {
        existingStyle.remove();
      }
    };
  }, []);

  // 获取任务数据
  useEffect(() => {
    const fetchTaskData = async () => {
      setLoading(true);
      try {
        // 调用API获取任务详情
        const taskData = await actionTaskAPI.getById(taskId);
        console.log('获取到任务详情:', taskData);

        // 获取环境变量
        try {
          // 使用批量API一次性获取所有变量
          const batchVariables = await actionTaskAPI.getBatchVariables(taskId);

          // 将变量设置到任务数据中
          taskData.environment_variables = batchVariables.environmentVariables;
          taskData.agent_variables = batchVariables.agentVariables;

          console.log('批量获取变量成功，最后更新时间:', batchVariables.lastUpdated);
        } catch (error) {
          console.error('获取变量失败:', error);
          taskData.environment_variables = [];
          taskData.agent_variables = [];
        }

        setTask(taskData);

        // 获取消息历史
        try {
          // 先尝试通过会话获取消息
          const conversations = await conversationAPI.getConversations(taskId);

          if (conversations && conversations.length > 0) {
            // 设置活动会话ID（使用第一个会话）
            setActiveConversationId(conversations[0].id);

            const firstConversationMessages = await conversationAPI.getConversationMessages(
              taskId,
              conversations[0].id
            );
            setMessages(firstConversationMessages);
          } else {
            // 如果没有会话，设置空消息数组
            setActiveConversationId(null);
            setMessages([]);
          }
        } catch (error) {
          console.error('获取消息失败:', error);
          // 设置空消息数组
          setActiveConversationId(null);
          setMessages([]);
        }
      } catch (error) {
        console.error('获取任务详情失败:', error);
        message.error('加载任务详情失败: ' + error.message);
        setTask(null);
      } finally {
        setLoading(false);
      }
    };

    fetchTaskData();
  }, [taskId]);



  // 返回任务列表
  const handleBack = () => {
    navigate('/action-tasks/overview');
  };



  // 归档任务
  const handleTerminateTask = async () => {
    try {
      // 实际应用中调用API
      // await actionTaskAPI.updateStatus(taskId, 'terminated');
      setTask({...task, status: 'terminated'});
      message.success('任务已终止');
    } catch (error) {
      message.error('操作失败: ' + error.message);
    }
  };

  // 处理消息更新事件 - 更新消息数量
  const handleMessagesUpdated = (updatedMessages) => {
    // 如果提供了更新后的消息数组，使用它更新状态
    if (updatedMessages && Array.isArray(updatedMessages)) {
      // 检查是否有重复消息，如果有，使用最新的消息数组
      // 这是因为ActionTaskConversation组件可能传递的是当前消息数组加上新消息
      const messageIds = new Set();
      const uniqueMessages = [];

      // 从后向前遍历，保留最新的消息
      for (let i = updatedMessages.length - 1; i >= 0; i--) {
        const msg = updatedMessages[i];
        if (msg.id && !messageIds.has(msg.id)) {
          messageIds.add(msg.id);
          uniqueMessages.unshift(msg); // 添加到数组开头，保持原始顺序
        }
      }

      // 更新消息状态
      setMessages(uniqueMessages);
    }

    // 我们不在这里检查工具调用结果，而是在ActionTaskConversation组件中处理
  };

  // 刷新任务消息的回调函数，供监督者组件调用
  const handleRefreshTaskMessages = async () => {
    if (!activeConversationId) return;

    try {
      // 重新加载任务消息（包含监督者干预消息）
      const messagesData = await actionTaskAPI.getTaskMessages(task.id, activeConversationId);
      setMessages(messagesData);
      console.log('监督者干预后刷新任务消息成功');
    } catch (error) {
      console.error('刷新任务消息失败:', error);
    }
  };

  // 处理监督者干预，委托给任务会话组件处理
  const handleSupervisorIntervention = async (messageData) => {
    try {
      console.log('处理监督者干预，委托给任务会话组件:', messageData);

      // 通过ref调用任务会话组件的监督者干预方法
      if (conversationRef.current && conversationRef.current.sendSupervisorIntervention) {
        await conversationRef.current.sendSupervisorIntervention(
          messageData.content,
          messageData.target_agent_id
        );
      } else {
        console.error('无法调用任务会话组件的监督者干预方法');
      }
    } catch (error) {
      console.error('监督者干预失败:', error);
    }
  };

  // 智能体响应状态变化处理
  const handleAgentRespondingChange = (isResponding, agentId) => {
    // 更新当前响应的智能体ID
    setRespondingAgentId(isResponding ? agentId : null);

    // 当智能体停止响应时，可能是因为工具调用结果已处理完毕
    // 但我们不在这里触发变量刷新，而是在ActionTaskConversation组件中处理
  };

  if (loading) {
    return (
      <div className="action-task-detail-page">
        <style>{customStyles}</style>
        <style>{variableFlashStyle}</style>
        {/* 显示与实际页面一致的页面头部 */}
        <div className="page-header" style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Space>
            <Button
              icon={<LeftOutlined />}
              onClick={handleBack}
              disabled={true}
            >
              返回任务列表
            </Button>
            <Title level={3} style={{ margin: 0 }}>加载中...</Title>
            <Space size="small">
              <Tag color="blue" icon={<GlobalOutlined />}>
                加载中...
              </Tag>
              <Badge status="processing" text="加载中" />
            </Space>
          </Space>
          <Space>
            <Button icon={<ExportOutlined />} disabled={true}>
              导出数据
            </Button>
          </Space>
        </div>

        <Card>
          <div style={{ position: 'relative' }}>
            {/* 加载指示器 - 绝对定位，不影响布局 */}
            <div style={{
              position: 'absolute',
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)',
              zIndex: 1000,
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              gap: '12px'
            }}>
              <Spin size="large" />
              <div style={{ color: '#1677ff', fontSize: '14px' }}>加载任务详情</div>
            </div>

            {/* 页面框架 - 完全透明背景 */}
            <div style={{ opacity: 0.3 }}>
              <Row gutter={16} style={{ height: 'calc(100vh - 220px)', minHeight: '600px' }}>
                <Col
                  span={16}
                  style={{
                    height: '100%',
                    display: 'flex',
                    flexDirection: 'column',
                    position: 'relative'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
                    <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>
                      <MessageOutlined style={{ marginRight: 8 }} />
                      <Text strong style={{ fontSize: '16px' }}>任务交互记录</Text>
                    </div>
                    <Button type="text" icon={<MenuFoldOutlined />} disabled={true}>
                      隐藏侧边栏
                    </Button>
                  </div>
                  <div style={{ height: 'calc(100% - 40px)', position: 'relative' }}>
                    <div className="tab-content-container" style={{ height: '100%', overflow: 'hidden', position: 'relative' }}>
                      {/* 交互记录框架 */}
                    </div>
                  </div>
                </Col>

                <Col
                  span={8}
                  style={{
                    height: '100%',
                    overflowY: 'auto',
                    borderLeft: '1px solid #f0f0f0',
                    position: 'relative'
                  }}
                >
                  <Tabs
                    defaultActiveKey="info"
                    size="small"
                    tabPosition="top"
                    style={{ marginBottom: 16 }}
                    items={[
                      {
                        key: 'info',
                        label: <span><InfoCircleOutlined />任务信息</span>,
                        children: (
                          <>
                            <Card title="任务信息" style={{ marginBottom: 16, minHeight: '150px' }}>
                              {/* 任务信息框架 */}
                            </Card>
                            <Card title="统计概览" style={{ marginBottom: 16, minHeight: '100px' }}>
                              {/* 统计概览框架 */}
                            </Card>
                          </>
                        )
                      },
                      {
                        key: 'monitor',
                        label: <span><EnvironmentOutlined />任务监控</span>,
                        children: (
                          <>
                            <Card
                              title={<><EnvironmentOutlined /> 环境变量</>}
                              style={{ marginBottom: 16, minHeight: '150px' }}
                            >
                              {/* 环境变量框架 */}
                            </Card>
                            <Card
                              title={<><TeamOutlined /> 参与智能体</>}
                              style={{ marginBottom: 16, minHeight: '200px' }}
                            >
                              {/* 参与智能体框架 */}
                            </Card>
                            <Card
                              title={<><EyeOutlined /> 监督者智能体</>}
                              style={{ marginBottom: 16, minHeight: '150px' }}
                            >
                              {/* 监督者智能体框架 */}
                            </Card>
                          </>
                        )
                      },
                      {
                        key: 'memory',
                        label: <span><BookOutlined />工作空间</span>,
                        children: (
                          <div style={{ minHeight: '300px' }}>
                            {task && <ActionTaskWorkspace task={task} key={`workspace-main-${task.id}`} />}
                          </div>
                        )
                      },
                      {
                        key: 'audit',
                        label: <span><ApartmentOutlined />审计日志</span>,
                        children: <div style={{ minHeight: '300px' }} />
                      }
                    ]}
                  />
                </Col>
              </Row>
            </div>
          </div>
        </Card>
      </div>
    );
  }

  if (!task) {
    return (
      <Result
        status="404"
        title="任务不存在"
        subTitle="抱歉，未找到指定的行动任务"
        extra={
          <Button type="primary" onClick={handleBack}>
            返回任务列表
          </Button>
        }
      />
    );
  }

  return (
    <div className="action-task-detail-page">
      <style>{customStyles}</style>
      <style>{variableFlashStyle}</style>
      <div className="page-header" style={{ marginBottom: 16, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Space>
          <Button
            icon={<LeftOutlined />}
            onClick={handleBack}
          >
            返回任务列表
          </Button>
          <Title level={3} style={{ margin: 0 }}>{task.title}</Title>
          <Space size="small">
            <Tag color="blue" icon={<GlobalOutlined />}>
              {task.action_space ? task.action_space.name : (task.action_space_name || '未指定行动空间')}
            </Tag>
            {task.status === 'active' && (
              <Badge status="processing" text="进行中" />
            )}

            {task.status === 'completed' && (
              <Badge status="success" text="已完成" />
            )}
            {task.status === 'terminated' && (
              <Badge status="error" text="已终止" />
            )}
          </Space>
        </Space>
        <Space>
          {task.status === 'active' && (
            <Button
              icon={<StopOutlined />}
              danger
              onClick={handleTerminateTask}
            >
              归档任务
            </Button>
          )}
          <Button
            icon={<ExportOutlined />}
          >
            导出数据
          </Button>
        </Space>
      </div>

      <Card>
        <Row gutter={16} style={{ height: 'calc(100vh - 180px)', minHeight: '600px' }} className={isDragging ? 'dragging-active' : ''}>
          <Col
            span={sidebarVisible ? leftColSpan : 24}
            style={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              position: 'relative',
              transition: isDragging ? 'none' : 'width 0.3s ease'
            }}
          >
            <div style={{ display: 'flex', alignItems: 'center', marginBottom: 8 }}>
              <div style={{ flex: 1, display: 'flex', alignItems: 'center' }}>
                <MessageOutlined style={{ marginRight: 8 }} />
                <Text strong style={{ fontSize: '16px' }}>任务交互记录</Text>
              </div>
              <Button
                type="text"
                icon={sidebarVisible ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />}
                onClick={toggleSidebar}
              >
                {sidebarVisible ? '隐藏侧边栏' : '显示侧边栏'}
              </Button>
            </div>
            <div style={{ height: 'calc(100% - 40px)', position: 'relative' }}>
              <div className="tab-content-container" style={{ height: '100%', overflow: 'hidden', position: 'relative' }}>
                {task && <ActionTaskConversation
                  ref={conversationRef}
                  task={task}
                  messages={messages}
                  setMessages={setMessages}
                  onMessagesUpdated={handleMessagesUpdated}
                  onAgentRespondingChange={handleAgentRespondingChange}
                  onUserMessageSent={refreshVariables}
                  onRefreshAutonomousTaskCard={refreshComponent}
                />}
              </div>
            </div>
            {/* 拖拽分隔线 - 放在Col内部右侧边缘 */}
            {sidebarVisible && (
              <div
                ref={dragHandleRef}
                className={`drag-handle ${isDragging ? 'dragging' : ''}`}
                style={{
                  position: 'absolute',
                  right: '-4px',
                  top: 0,
                  bottom: 0,
                  zIndex: 1000
                }}
                onMouseDown={handleDragStart}
              ></div>
            )}
          </Col>

          {/* 使用Col组件替代Drawer组件 */}
          {sidebarVisible && (
            <Col
              span={rightColSpan}
              style={{
                height: '100%',
                overflowY: 'auto',
                borderLeft: '1px solid #f0f0f0',
                transition: isDragging ? 'none' : 'width 0.3s ease',
                position: 'relative'
              }}
            >
              <Tabs
                activeKey={activeSidebarTab}
                onChange={handleSidebarTabChange}
                size="small"
                tabPosition="top"
                style={{ marginBottom: 16 }}
                items={[
                  {
                    key: 'info',
                    label: <span><InfoCircleOutlined />任务信息</span>,
                  },
                  {
                    key: 'monitor',
                    label: <span><EnvironmentOutlined />任务监控</span>,
                  },
                  {
                    key: 'memory',
                    label: <span><BranchesOutlined />工作空间</span>,
                  },
                  {
                    key: 'audit',
                    label: <span><ApartmentOutlined />监督会话</span>,
                  }
                ]}
              />
            {/* 任务信息标签页 */}
            {activeSidebarTab === 'info' && (
              <>
                <Card title="统计概览" style={{ marginBottom: 16 }}>
                  <Row gutter={16}>
                    <Col span={12}>
                      <Statistic
                        title="消息总数"
                        value={messages.length}
                        prefix={<MessageOutlined />}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="智能体数量"
                        value={task.agents?.length || 0}
                        prefix={<TeamOutlined />}
                      />
                    </Col>
                  </Row>
                </Card>

                <Card title="任务信息" style={{ marginBottom: 16 }}>
                  <Descriptions column={1} size="small">
                    <Descriptions.Item label="任务ID">
                      {task.id}
                    </Descriptions.Item>
                    <Descriptions.Item label="行动空间">
                      {task.action_space ? task.action_space.name : (task.action_space_name || '未指定')}
                    </Descriptions.Item>
                    <Descriptions.Item label="任务描述">
                      {task.description || '无描述'}
                    </Descriptions.Item>
                    <Descriptions.Item label="创建时间">
                      {new Date(task.created_at).toLocaleString()}
                    </Descriptions.Item>
                    <Descriptions.Item label="最后更新">
                      {new Date(task.updated_at).toLocaleString()}
                    </Descriptions.Item>
                  </Descriptions>
                </Card>
              </>
            )}

            {/* 任务监控标签页 */}
            {activeSidebarTab === 'monitor' && (
              <>
                <Card
                  title={<><EnvironmentOutlined /> 环境变量</>}
                  style={{ marginBottom: 16 }}
                  styles={{ body: { padding: '12px 16px' } }}
                >
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      行动任务环境变量（与行动任务相关的系统级变量）
                    </Text>
                  </div>
                  {task && <ActionTaskEnvironment task={task} key={`env-vars-${variablesRefreshKey}`} />}
                </Card>

                {/* 自主行动卡片 */}
                {activeConversationId && (
                  <AutonomousTaskCard
                    task={task}
                    activeConversationId={activeConversationId}
                    refreshKey={refreshKey}
                  />
                )}

                <Card
                  title={<><TeamOutlined /> 参与智能体</>}
                  styles={{ body: { padding: '12px 8px' } }}
                  style={{ marginBottom: 16 }}
                >
                  {task.agents && task.agents.length > 0 ? (
                    <List
                      dataSource={task.agents.filter(agent => !agent.is_observer && agent.type !== 'observer')}
                      itemLayout="horizontal"
                      split={true}
                      renderItem={agent => {
                        // 计算该智能体的历史消息数量（按agent_id统计）
                        const agentMessages = messages.filter(m => m.agent_id === agent.id).length;
                        // 获取触发规则数量（从后端数据获取，如果没有则为0）
                        const ruleTriggersCount = agent.rule_triggers_count || 0;
                        // 计算该智能体的工具调用数量
                        const toolCallsCount = messages.reduce((count, message) => {
                          // 只统计该智能体的消息（按agent_id统计）
                          if (message.agent_id === agent.id) {
                            // 检查消息内容中是否包含工具调用
                            const content = message.content || '';
                            // 检查是否包含工具调用的标志
                            const hasToolCall =
                              (content.includes('"meta":{"ToolCallAction"') ||
                               content.includes('"toolName"') && content.includes('"toolCallId"') ||
                               content.includes('"function":') && content.includes('"arguments":'));

                            // 如果包含工具调用，增加计数
                            if (hasToolCall) {
                              // 尝试计算消息中的工具调用数量
                              let toolCallMatches = 0;

                              // 匹配ToolCallAction
                              const toolCallActionMatches = content.match(/"meta":\s*{\s*"ToolCallAction"/g);
                              if (toolCallActionMatches) {
                                toolCallMatches += toolCallActionMatches.length;
                              }

                              // 匹配toolName和toolCallId组合
                              const toolNameMatches = content.match(/"toolName".*?"toolCallId"/g);
                              if (toolNameMatches) {
                                toolCallMatches += toolNameMatches.length;
                              }

                              // 匹配function和arguments组合
                              const functionMatches = content.match(/"function".*?"arguments"/g);
                              if (functionMatches) {
                                toolCallMatches += functionMatches.length;
                              }

                              // 如果没有匹配到具体数量，至少计为1次
                              return count + (toolCallMatches > 0 ? toolCallMatches : 1);
                            }
                          }
                          return count;
                        }, 0);
                        // 检查当前智能体是否正在响应
                        const isResponding = String(agent.id) === String(respondingAgentId);

                        // 获取该智能体的变量
                        const agentVars = task.agent_variables ?
                          task.agent_variables.filter(v => v.agent_id === agent.id) : [];

                        return (
                          <List.Item style={{
                            borderRadius: '8px',
                            padding: '4px 6px',
                            marginBottom: '4px'
                          }}>
                            <div style={{ width: '100%', padding: '4px 2px' }}>
                              <div style={{ display: 'flex', width: '100%' }}>
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                  <Avatar
                                    icon={<RobotOutlined style={{ color: '#ffffff' }} />}
                                    style={{
                                      ...getAgentAvatarStyle(agent.id || agent.name, isResponding),
                                      marginRight: '12px'
                                    }}
                                  />
                                </div>
                                <div style={{ flex: 1 }}>
                                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <Text strong>{agent.role_name ? `${agent.name} [${agent.role_name}]` : agent.name}</Text>
                                    <Space size="small" style={{ position: 'relative', zIndex: 10 }}>
                                      <Tooltip title="历史消息数量">
                                        <Badge count={agentMessages} style={{ backgroundColor: '#1890ff' }}>
                                          <MessageOutlined style={{ fontSize: '16px', color: '#8c8c8c' }} />
                                        </Badge>
                                      </Tooltip>
                                      <Tooltip title="触发规则数量">
                                        <Badge count={ruleTriggersCount} style={{ backgroundColor: '#faad14' }}>
                                          <ApartmentOutlined style={{ fontSize: '16px', color: '#8c8c8c' }} />
                                        </Badge>
                                      </Tooltip>
                                      <Tooltip title="调用工具数量">
                                        <Badge count={toolCallsCount} style={{ backgroundColor: '#722ed1' }}>
                                          <ToolOutlined style={{ fontSize: '16px', color: '#8c8c8c' }} />
                                        </Badge>
                                      </Tooltip>
                                    </Space>
                                  </div>
                                  <div style={{ marginTop: '4px' }}>
                                    <Text type="secondary">{agent.description || '无描述'}</Text>
                                  </div>
                                </div>
                              </div>

                              {/* 智能体变量 */}
                              {agentVars.length > 0 && (
                                <div style={{ marginTop: 8 }}>
                                  <Text type="secondary" style={{ fontSize: '12px', marginBottom: 4, display: 'block' }}>
                                    智能体变量
                                  </Text>
                                  <Table
                                    dataSource={agentVars.map((v, index) => ({
                                      ...v,
                                      key: v.id || `${v.name}-${agent.id}-${index}`
                                    }))}
                                    size="small"
                                    pagination={false}
                                    style={{ marginTop: 4 }}
                                    key={`agent-vars-${agent.id}-${variablesRefreshKey}`}
                                    rowKey={record => record.key || record.id || `${record.name}-${agent.id}`}
                                    columns={[
                                      {
                                        title: '变量名称',
                                        dataIndex: 'name',
                                        key: 'name',
                                        ellipsis: true,
                                        width: '30%',
                                      },
                                      {
                                        title: '变量标签',
                                        dataIndex: 'label',
                                        key: 'label',
                                        ellipsis: true,
                                        width: '30%',
                                      },
                                      {
                                        title: '当前值',
                                        dataIndex: 'value',
                                        key: 'value',
                                        width: '40%',
                                        render: (value, record) => {
                                          // 如果值为空，不显示任何内容
                                          if (value === undefined || value === null || value === '') {
                                            return (
                                              <div>
                                                <span></span>
                                              </div>
                                            );
                                          }

                                          let displayValue = value;
                                          let color = 'blue';

                                          // 根据类型格式化显示
                                          if (record.type === 'boolean') {
                                            displayValue = value ? '是' : '否';
                                            color = value ? 'green' : 'red';
                                          } else if (record.type === 'number') {
                                            displayValue = `${value}${record.unit || ''}`;
                                            // 如果是百分比类型的数值，根据值设置颜色
                                            if (record.unit === '%' || record.unit === '百分比') {
                                              color = value > 70 ? 'red' : value > 30 ? 'orange' : 'green';
                                            }
                                          } else if (record.type === 'date') {
                                            displayValue = new Date(value).toLocaleDateString();
                                          }

                                          // 检查变量是否有变化标记或是新变量，如果是则添加闪烁效果的类名
                                          const hasChanged = record._hasChanged === true;
                                          const isNew = record._isNew === true;

                                          return (
                                            <div className={hasChanged ? 'variable-flash' : ''}>
                                              <Tag color={color}>
                                                {displayValue}
                                                {isNew && <span style={{ marginLeft: 4, color: '#52c41a', fontWeight: 'bold' }}>(新)</span>}
                                              </Tag>
                                            </div>
                                          );
                                        },
                                      }
                                    ]}
                                  />
                                </div>
                              )}
                            </div>
                          </List.Item>
                        );
                      }}
                    />
                  ) : (
                    <Empty description="暂无智能体数据" />
                  )}
                </Card>

                {/* 监督者智能体卡片 */}
                <Card
                  title={<><EyeOutlined /> 监督者智能体</>}
                  styles={{ body: { padding: '12px 8px' } }}
                  style={{ marginBottom: 16 }}
                >
                  {task.agents && task.agents.filter(agent => agent.is_observer || agent.type === 'observer').length > 0 ? (
                    <List
                      dataSource={task.agents.filter(agent => agent.is_observer || agent.type === 'observer')}
                      itemLayout="horizontal"
                      split={true}
                      renderItem={agent => {
                        // 计算该智能体的历史消息数量（按agent_id统计）
                        const agentMessages = messages.filter(m => m.agent_id === agent.id).length;
                        // 获取触发规则数量（从后端数据获取，如果没有则为0）
                        const ruleTriggersCount = agent.rule_triggers_count || 0;
                        // 计算该智能体的工具调用数量
                        const toolCallsCount = messages.reduce((count, message) => {
                          // 只统计该智能体的消息（按agent_id统计）
                          if (message.agent_id === agent.id) {
                            // 检查消息内容中是否包含工具调用
                            const content = message.content || '';
                            // 检查是否包含工具调用的标志
                            const hasToolCall =
                              (content.includes('"meta":{"ToolCallAction"') ||
                               content.includes('"toolName"') && content.includes('"toolCallId"') ||
                               content.includes('"function":') && content.includes('"arguments":'));

                            // 如果包含工具调用，增加计数
                            if (hasToolCall) {
                              // 尝试计算消息中的工具调用数量
                              let toolCallMatches = 0;

                              // 匹配ToolCallAction
                              const toolCallActionMatches = content.match(/"meta":\s*{\s*"ToolCallAction"/g);
                              if (toolCallActionMatches) {
                                toolCallMatches += toolCallActionMatches.length;
                              }

                              // 匹配toolName和toolCallId组合
                              const toolNameMatches = content.match(/"toolName".*?"toolCallId"/g);
                              if (toolNameMatches) {
                                toolCallMatches += toolNameMatches.length;
                              }

                              // 匹配function和arguments组合
                              const functionMatches = content.match(/"function".*?"arguments"/g);
                              if (functionMatches) {
                                toolCallMatches += functionMatches.length;
                              }

                              // 如果没有匹配到具体数量，至少计为1次
                              return count + (toolCallMatches > 0 ? toolCallMatches : 1);
                            }
                          }
                          return count;
                        }, 0);
                        // 检查当前智能体是否正在响应
                        const isResponding = String(agent.id) === String(respondingAgentId);

                        // 获取该智能体的变量
                        const agentVars = task.agent_variables ?
                          task.agent_variables.filter(v => v.agent_id === agent.id) : [];

                        return (
                          <List.Item style={{
                            borderRadius: '8px',
                            padding: '4px 6px',
                            marginBottom: '4px',
                            backgroundColor: '#f6f8fa'  // 监督者背景色略有不同
                          }}>
                            <div style={{ width: '100%', padding: '4px 2px' }}>
                              <div style={{ display: 'flex', width: '100%' }}>
                                <div style={{ display: 'flex', alignItems: 'center' }}>
                                  <Avatar
                                    icon={<EyeOutlined style={{ color: '#ffffff' }} />}
                                    style={{
                                      ...getAgentAvatarStyle(agent.id || agent.name, isResponding, true),
                                      marginRight: '12px'
                                    }}
                                  />
                                </div>
                                <div style={{ flex: 1 }}>
                                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <Text strong>{agent.role_name ? `${agent.name} [${agent.role_name}]` : agent.name}</Text>
                                    <Space size="small" style={{ position: 'relative', zIndex: 10 }}>
                                      <Tooltip title="历史消息数量">
                                        <Badge count={agentMessages} style={{ backgroundColor: '#1890ff' }}>
                                          <MessageOutlined style={{ fontSize: '16px', color: '#8c8c8c' }} />
                                        </Badge>
                                      </Tooltip>
                                      <Tooltip title="触发规则数量">
                                        <Badge count={ruleTriggersCount} style={{ backgroundColor: '#faad14' }}>
                                          <ApartmentOutlined style={{ fontSize: '16px', color: '#8c8c8c' }} />
                                        </Badge>
                                      </Tooltip>
                                      <Tooltip title="调用工具数量">
                                        <Badge count={toolCallsCount} style={{ backgroundColor: '#722ed1' }}>
                                          <ToolOutlined style={{ fontSize: '16px', color: '#8c8c8c' }} />
                                        </Badge>
                                      </Tooltip>
                                    </Space>
                                  </div>
                                  <div style={{ marginTop: '4px' }}>
                                    <Text type="secondary">{agent.description || '无描述'}</Text>
                                  </div>
                                </div>
                              </div>

                              {/* 智能体变量 */}
                              {agentVars.length > 0 && (
                                <div style={{ marginTop: 8 }}>
                                  <Text type="secondary" style={{ fontSize: '12px', marginBottom: 4, display: 'block' }}>
                                    监督者变量
                                  </Text>
                                  <Table
                                    dataSource={agentVars.map((v, index) => ({
                                      ...v,
                                      key: v.id || `${v.name}-${agent.id}-${index}`
                                    }))}
                                    size="small"
                                    pagination={false}
                                    style={{ marginTop: 4 }}
                                    key={`observer-vars-${agent.id}-${variablesRefreshKey}`}
                                    rowKey={record => record.key || record.id || `${record.name}-${agent.id}`}
                                    columns={[
                                      {
                                        title: '变量名称',
                                        dataIndex: 'name',
                                        key: 'name',
                                        ellipsis: true,
                                        width: '30%',
                                      },
                                      {
                                        title: '变量标签',
                                        dataIndex: 'label',
                                        key: 'label',
                                        ellipsis: true,
                                        width: '30%',
                                      },
                                      {
                                        title: '当前值',
                                        dataIndex: 'value',
                                        key: 'value',
                                        width: '40%',
                                        render: (value, record) => {
                                          // 如果值为空，不显示任何内容
                                          if (value === undefined || value === null || value === '') {
                                            return (
                                              <div>
                                                <span></span>
                                              </div>
                                            );
                                          }

                                          let displayValue = value;
                                          let color = 'purple';  // 监督者变量使用紫色

                                          // 根据类型格式化显示
                                          if (record.type === 'boolean') {
                                            displayValue = value ? '是' : '否';
                                            color = value ? 'green' : 'red';
                                          } else if (record.type === 'number') {
                                            displayValue = `${value}${record.unit || ''}`;
                                            // 如果是百分比类型的数值，根据值设置颜色
                                            if (record.unit === '%' || record.unit === '百分比') {
                                              color = value > 70 ? 'red' : value > 30 ? 'orange' : 'green';
                                            }
                                          } else if (record.type === 'date') {
                                            displayValue = new Date(value).toLocaleDateString();
                                          }

                                          // 检查变量是否有变化标记或是新变量，如果是则添加闪烁效果的类名
                                          const hasChanged = record._hasChanged === true;
                                          const isNew = record._isNew === true;

                                          return (
                                            <div className={hasChanged ? 'variable-flash' : ''}>
                                              <Tag color={color}>
                                                {displayValue}
                                                {isNew && <span style={{ marginLeft: 4, color: '#52c41a', fontWeight: 'bold' }}>(新)</span>}
                                              </Tag>
                                            </div>
                                          );
                                        },
                                      }
                                    ]}
                                  />
                                </div>
                              )}
                            </div>
                          </List.Item>
                        );
                      }}
                    />
                  ) : (
                    <Empty description="暂无监督者智能体" />
                  )}
                </Card>
              </>
            )}

            {/* 工作空间标签页 */}
            {activeSidebarTab === 'memory' && (
              <div style={{ marginBottom: 16 }}>
                {task && <ActionTaskWorkspace task={task} key={`workspace-card-${task.id}`} />}
              </div>
            )}

            {/* 监督会话标签页 */}
            {activeSidebarTab === 'audit' && (
              <>
                <Card
                  title={<><ApartmentOutlined /> 规则记录</>}
                  style={{ marginBottom: 16 }}
                  styles={{ body: { padding: '12px 16px' } }}
                >
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      行动任务中触发的规则记录，支持手动检查当前任务的规则合规性
                    </Text>
                  </div>
                  {task && <ActionTaskRules
                    task={{...task, conversation_id: activeConversationId}}
                    key={`rules-${refreshKey}`}
                  />}
                </Card>

                <Card
                  title={<><EyeOutlined /> 监督会话</>}
                  style={{ marginBottom: 16 }}
                  styles={{ body: { padding: '12px 16px' } }}
                >
                  <div style={{ marginBottom: 8 }}>
                    <Text type="secondary" style={{ fontSize: '12px' }}>
                      监督者与用户的交互会话记录
                    </Text>
                  </div>
                  {task && <ActionTaskSupervisor
                    task={{...task, conversation_id: activeConversationId}}
                    onTaskMessagesRefresh={handleRefreshTaskMessages}
                    onSupervisorIntervention={handleSupervisorIntervention}
                    key={`supervisor-${refreshKey}`}
                  />}
                </Card>
              </>
            )}




            </Col>
          )}
        </Row>
      </Card>
    </div>
  );
};

export default ActionTaskDetail;