"""
系统设置API路由

处理与系统设置相关的所有API请求
"""
from flask import Blueprint, request, jsonify, current_app
from app.models import SystemSetting
from app.extensions import db

# 默认提示词模板定义（统一管理，避免重复）
DEFAULT_PROMPT_TEMPLATES = {
    'roleSystemPrompt': '''请根据以下角色信息生成一个专业的系统提示词：

角色名称：{{name}}
角色描述：{{description}}

要求：
1. 系统提示词应该清晰地定义角色的身份、专业领域和能力
2. 包含角色的行为准则和回答风格
3. 明确角色的职责范围和限制
4. 使用专业、准确的语言
5. 长度适中，既要详细又要简洁

请直接返回系统提示词内容，不需要额外的解释。''',

    'actionSpaceBackground': '''请根据以下多智能体行动空间信息生成专业的背景设定：

行动空间名称：{{name}}
行动空间描述：{{description}}

要求：
1. 背景设定应该详细描述行动空间的环境、场景和上下文
2. 包含相关的历史背景、现状分析和发展趋势
3. 明确行动空间的目标和意义
4. 使用生动、具体的语言描述
5. 为参与者提供充分的情境信息

请直接返回背景设定内容，不需要额外的解释。''',

    'actionSpaceRules': '''请根据以下行动空间信息生成专业的基本规则：

行动空间名称：{{name}}
行动空间描述：{{description}}

要求：
1. 基本规则应该明确定义行动空间内的行为准则和约束条件
2. 包含参与者的权限和责任范围
3. 规定交互方式和协作机制
4. 明确决策流程和执行标准
5. 包含风险控制和异常处理规则
6. 使用清晰、准确的语言表述
7. 条理清晰，便于理解和执行

请直接返回基本规则内容，不需要额外的解释。''',

    'actionTaskDescription': '''请根据以下信息生成详细的行动任务描述：

任务名称：{{title}}
行动空间名称：{{action_space_name}}
行动空间描述：{{action_space_description}}
参与角色：{{roles}}

要求：
1. 任务描述应该明确任务的目标和预期成果
2. 详细说明任务的执行步骤和关键节点
3. 明确各角色的职责分工和协作方式
4. 包含任务的评估标准和成功指标
5. 考虑可能的风险和应对措施
6. 使用清晰、具体的语言描述
7. 确保任务的可执行性和可衡量性

请直接返回任务描述内容，不需要额外的解释。''',

    'userMessageExpand': '''请根据以下信息扩展和优化用户消息：

原始消息：{{original_message}}
当前行动空间：{{action_space_name}}
行动空间描述：{{action_space_description}}
参与角色：{{participant_roles}}
辅助模式：{{assist_mode}}

要求：
1. 保持原始消息的核心意图和目的
2. 根据行动空间背景和参与角色，调整语言风格和专业程度
3. 补充必要的上下文信息和细节
4. 确保消息清晰、具体、易于理解
5. 根据辅助模式进行相应的处理：
   - expand: 扩展内容，增加细节和背景信息
   - optimize: 优化表达，改善语言和逻辑结构
   - rewrite: 重新表述，用不同方式表达相同意思
   - professional: 专业化表达，提升正式程度
   - casual: 口语化表达，使语言更自然亲切

请直接返回优化后的消息内容，不需要额外的解释。'''
}

# 创建Blueprint
settings_bp = Blueprint('settings_api', __name__)

@settings_bp.route('/settings', methods=['GET'])
def get_settings():
    """获取系统设置"""
    # 初始化空字典，稍后填充
    settings_dict = {}

    # 从数据库获取所有系统设置
    db_settings = SystemSetting.query.all()

    # 记录日志，帮助调试
    current_app.logger.info(f"从数据库获取到 {len(db_settings)} 个系统设置")

    # 首先处理数据库中的设置，确保它们的优先级高于app.config
    for setting in db_settings:
        key = setting.key

        # 记录每个设置的键值对，帮助调试
        current_app.logger.debug(f"处理系统设置: {key} = {setting.value} (类型: {setting.value_type})")

        # 将下划线命名转为驼峰命名（前端使用）
        if key == 'includeThinkingContentInContext':
            frontend_key = key  # 已经是驼峰命名
        else:
            parts = key.split('_')
            frontend_key = parts[0] + ''.join(p.capitalize() for p in parts[1:])

        # 根据值类型进行转换
        if setting.value_type == 'boolean':
            settings_dict[frontend_key] = setting.value.lower() in ('true', '1', 'yes')
        elif setting.value_type == 'number':
            try:
                if '.' in setting.value:
                    settings_dict[frontend_key] = float(setting.value)
                else:
                    settings_dict[frontend_key] = int(setting.value)
            except (ValueError, TypeError):
                settings_dict[frontend_key] = 0
        elif setting.value_type == 'json':
            try:
                import json
                settings_dict[frontend_key] = json.loads(setting.value)
            except (ValueError, TypeError):
                settings_dict[frontend_key] = {}
        else:
            settings_dict[frontend_key] = setting.value

        # 同时更新app.config，确保app.config与数据库同步
        config_key = key.upper()
        if setting.value_type == 'boolean':
            current_app.config[config_key] = settings_dict[frontend_key]
        elif setting.value_type == 'number':
            current_app.config[config_key] = settings_dict[frontend_key]
        elif setting.value_type == 'json':
            current_app.config[config_key] = settings_dict[frontend_key]
        else:
            current_app.config[config_key] = setting.value

        current_app.logger.debug(f"已更新app.config: {config_key} = {current_app.config.get(config_key)}")

    # 然后添加app.config中的基础配置（如果数据库中没有）
    # 注意：这里使用驼峰命名，与前端期望的字段名一致
    if "apiUrl" not in settings_dict:
        settings_dict["apiUrl"] = current_app.config.get("API_URL", "")
    if "model" not in settings_dict:
        settings_dict["model"] = current_app.config.get("DEFAULT_MODEL", "")
    if "temperature" not in settings_dict:
        settings_dict["temperature"] = current_app.config.get("TEMPERATURE", 0.7)
    if "maxTokens" not in settings_dict:
        settings_dict["maxTokens"] = current_app.config.get("MAX_TOKENS", 2000)
    if "systemPrompt" not in settings_dict:
        settings_dict["systemPrompt"] = current_app.config.get("SYSTEM_PROMPT", "你是一个拥有多种角色的模拟对话系统。请按照世界设定和角色特性进行回应。")
    if "enableDebugMode" not in settings_dict:
        settings_dict["enableDebugMode"] = current_app.config.get("DEBUG", False)
    # 向量数据库设置默认值
    if "useBuiltinVectorDB" not in settings_dict:
        settings_dict["useBuiltinVectorDB"] = current_app.config.get("USE_BUILTIN_VECTOR_DB", True)
    if "vectorDBProvider" not in settings_dict:
        settings_dict["vectorDBProvider"] = current_app.config.get("VECTOR_DB_PROVIDER", "aliyun")
    if "vectorDBConfig" not in settings_dict:
        settings_dict["vectorDBConfig"] = current_app.config.get("VECTOR_DB_CONFIG", {})
    # 辅助生成设置默认值
    if "enableAssistantGeneration" not in settings_dict:
        settings_dict["enableAssistantGeneration"] = current_app.config.get("ENABLE_ASSISTANT_GENERATION", True)
    if "assistantGenerationModel" not in settings_dict:
        settings_dict["assistantGenerationModel"] = current_app.config.get("ASSISTANT_GENERATION_MODEL", "default")
    # 超时配置默认值
    if "httpConnectionTimeout" not in settings_dict:
        settings_dict["httpConnectionTimeout"] = current_app.config.get("HTTP_CONNECTION_TIMEOUT", 30)
    if "httpReadTimeout" not in settings_dict:
        settings_dict["httpReadTimeout"] = current_app.config.get("HTTP_READ_TIMEOUT", 300)
    if "streamSocketTimeout" not in settings_dict:
        settings_dict["streamSocketTimeout"] = current_app.config.get("STREAM_SOCKET_TIMEOUT", 60)
    if "defaultModelTimeout" not in settings_dict:
        settings_dict["defaultModelTimeout"] = current_app.config.get("DEFAULT_MODEL_TIMEOUT", 60)

    # 为了兼容性，添加下划线版本的字段（旧版前端可能仍在使用）
    # 这些字段将在未来版本中移除
    settings_dict["api_url"] = settings_dict.get("apiUrl", "")
    settings_dict["max_tokens"] = settings_dict.get("maxTokens", 2000)
    settings_dict["system_prompt"] = settings_dict.get("systemPrompt", "你是一个拥有多种角色的模拟对话系统。请按照世界设定和角色特性进行回应。")
    settings_dict["enable_debug_mode"] = settings_dict.get("enableDebugMode", False)
    settings_dict["max_conversation_history_length"] = settings_dict.get("maxConversationHistoryLength", 10)
    settings_dict["streaming_enabled"] = settings_dict.get("streamingEnabled", True)
    settings_dict["store_llm_error_messages"] = settings_dict.get("storeLlmErrorMessages", True)
    settings_dict["include_thinking_content_in_context"] = settings_dict.get("includeThinkingContentInContext", True)
    # 向量数据库设置的下划线版本
    settings_dict["use_builtin_vector_db"] = settings_dict.get("useBuiltinVectorDB", True)
    settings_dict["vector_db_provider"] = settings_dict.get("vectorDBProvider", "aliyun")
    # 辅助生成设置的下划线版本
    settings_dict["enable_assistant_generation"] = settings_dict.get("enableAssistantGeneration", True)
    settings_dict["assistant_generation_model"] = settings_dict.get("assistantGenerationModel", "default")

    # 记录最终返回的设置
    current_app.logger.debug(f"返回给前端的系统设置: {settings_dict}")
    return jsonify(settings_dict)

@settings_bp.route('/settings', methods=['POST'])
def update_settings():
    """更新系统设置"""
    data = request.get_json()
    current_app.logger.debug(f"收到更新系统设置请求: {data}")

    # 创建映射：前端参数名到数据库键名和app.config键名
    # 格式: 'frontendKey': {'db_key': 'database_key', 'config_key': 'CONFIG_KEY', 'value_type': 'string|number|boolean'}
    settings_map = {
        'apiUrl': {'db_key': 'api_url', 'config_key': 'API_URL', 'value_type': 'string'},
        'temperature': {'db_key': 'temperature', 'config_key': 'TEMPERATURE', 'value_type': 'number'},
        'maxTokens': {'db_key': 'max_tokens', 'config_key': 'MAX_TOKENS', 'value_type': 'number'},
        'systemPrompt': {'db_key': 'system_prompt', 'config_key': 'SYSTEM_PROMPT', 'value_type': 'string'},
        'enableDebugMode': {'db_key': 'enable_debug_mode', 'config_key': 'DEBUG', 'value_type': 'boolean'},
        'maxConversationHistoryLength': {'db_key': 'max_conversation_history_length', 'config_key': 'MAX_CONVERSATION_HISTORY_LENGTH', 'value_type': 'number'},
        'streamingEnabled': {'db_key': 'streaming_enabled', 'config_key': 'STREAMING_ENABLED', 'value_type': 'boolean'},
        'timezone': {'db_key': 'timezone', 'config_key': 'TIMEZONE', 'value_type': 'string'},
        'storeLlmErrorMessages': {'db_key': 'store_llm_error_messages', 'config_key': 'STORE_LLM_ERROR_MESSAGES', 'value_type': 'boolean'},
        'includeThinkingContentInContext': {'db_key': 'includeThinkingContentInContext', 'config_key': 'INCLUDE_THINKING_CONTENT_IN_CONTEXT', 'value_type': 'boolean'},
        'splitToolCallsInHistory': {'db_key': 'splitToolCallsInHistory', 'config_key': 'SPLIT_TOOL_CALLS_IN_HISTORY', 'value_type': 'boolean'},
        # 向量数据库设置
        'useBuiltinVectorDB': {'db_key': 'use_builtin_vector_db', 'config_key': 'USE_BUILTIN_VECTOR_DB', 'value_type': 'boolean'},
        'vectorDBProvider': {'db_key': 'vector_db_provider', 'config_key': 'VECTOR_DB_PROVIDER', 'value_type': 'string'},
        # 向量数据库配置
        'vectorDBConfig': {'db_key': 'vector_db_config', 'config_key': 'VECTOR_DB_CONFIG', 'value_type': 'json'},
        # 辅助生成设置
        'enableAssistantGeneration': {'db_key': 'enable_assistant_generation', 'config_key': 'ENABLE_ASSISTANT_GENERATION', 'value_type': 'boolean'},
        'assistantGenerationModel': {'db_key': 'assistant_generation_model', 'config_key': 'ASSISTANT_GENERATION_MODEL', 'value_type': 'string'},
        # 超时配置设置
        'httpConnectionTimeout': {'db_key': 'http_connection_timeout', 'config_key': 'HTTP_CONNECTION_TIMEOUT', 'value_type': 'number'},
        'httpReadTimeout': {'db_key': 'http_read_timeout', 'config_key': 'HTTP_READ_TIMEOUT', 'value_type': 'number'},
        'streamSocketTimeout': {'db_key': 'stream_socket_timeout', 'config_key': 'STREAM_SOCKET_TIMEOUT', 'value_type': 'number'},
        'defaultModelTimeout': {'db_key': 'default_model_timeout', 'config_key': 'DEFAULT_MODEL_TIMEOUT', 'value_type': 'number'}
    }

    # 处理每个设置
    for front_key, value in data.items():
        current_app.logger.debug(f"处理设置: {front_key} = {value}")

        # 检查是否是已知的设置
        if front_key in settings_map:
            setting_info = settings_map[front_key]
            db_key = setting_info['db_key']
            config_key = setting_info['config_key']
            value_type = setting_info['value_type']

            # 根据值类型进行转换
            if value_type == 'boolean':
                # 转换为字符串存储到数据库
                db_value = str(value).lower()
                # 转换为布尔值存储到app.config
                config_value = db_value.lower() in ('true', '1', 'yes')
            elif value_type == 'number':
                # 转换为字符串存储到数据库
                db_value = str(value)
                # 转换为数字存储到app.config
                try:
                    if '.' in db_value:
                        config_value = float(db_value)
                    else:
                        config_value = int(db_value)
                except (ValueError, TypeError):
                    config_value = 0
            elif value_type == 'json':
                # JSON类型：序列化为字符串存储到数据库
                import json
                db_value = json.dumps(value, ensure_ascii=False)
                # 保持原始对象存储到app.config
                config_value = value
            else:  # string
                db_value = value
                config_value = value

            # 获取当前设置信息
            setting = SystemSetting.query.filter_by(key=db_key).first()

            try:
                if setting:
                    # 更新现有设置
                    current_app.logger.debug(f"更新现有设置: {db_key} = {db_value}")
                    SystemSetting.set(
                        key=db_key,
                        value=db_value,
                        value_type=value_type,
                        description=setting.description,
                        category=setting.category,
                        is_secret=setting.is_secret
                    )
                else:
                    # 创建新设置
                    current_app.logger.debug(f"创建新设置: {db_key} = {db_value}")
                    SystemSetting.set(
                        key=db_key,
                        value=db_value,
                        value_type=value_type,
                        description=f"前端设置: {front_key}",
                        category="system"
                    )

                # 更新app.config
                current_app.config[config_key] = config_value
                current_app.logger.debug(f"已更新app.config: {config_key} = {config_value}")

            except Exception as e:
                current_app.logger.error(f"保存设置 {db_key} 失败: {str(e)}")
        else:
            current_app.logger.warning(f"未知的设置: {front_key} = {value}")

    return jsonify({
        'success': True,
        'message': '系统设置已更新'
    })

@settings_bp.route('/settings/prompt-templates', methods=['GET'])
def get_prompt_templates():
    """获取提示词模板"""
    try:

        # 从数据库获取自定义模板
        templates = {}
        template_keys = ['roleSystemPrompt', 'actionSpaceBackground', 'actionSpaceRules', 'actionTaskDescription', 'userMessageExpand']

        for key in template_keys:
            db_key = f'prompt_template_{key}'
            setting = SystemSetting.query.filter_by(key=db_key).first()
            if setting:
                templates[key] = setting.value
            else:
                templates[key] = DEFAULT_PROMPT_TEMPLATES[key]

        current_app.logger.debug(f"返回提示词模板: {list(templates.keys())}")
        return jsonify(templates)

    except Exception as e:
        current_app.logger.error(f"获取提示词模板失败: {str(e)}")
        return jsonify({"error": "获取提示词模板失败"}), 500



@settings_bp.route('/settings/prompt-templates', methods=['POST'])
def update_prompt_templates():
    """更新提示词模板"""
    try:
        data = request.get_json()
        current_app.logger.debug(f"收到更新提示词模板请求: {list(data.keys())}")

        # 模板键名映射
        template_keys = {
            'roleSystemPrompt': 'prompt_template_roleSystemPrompt',
            'actionSpaceBackground': 'prompt_template_actionSpaceBackground',
            'actionSpaceRules': 'prompt_template_actionSpaceRules',
            'actionTaskDescription': 'prompt_template_actionTaskDescription',
            'userMessageExpand': 'prompt_template_userMessageExpand'
        }

        # 更新每个模板
        for front_key, template_content in data.items():
            if front_key in template_keys:
                db_key = template_keys[front_key]

                # 获取或创建设置
                setting = SystemSetting.query.filter_by(key=db_key).first()

                if setting:
                    # 更新现有设置
                    SystemSetting.set(
                        key=db_key,
                        value=template_content,
                        value_type='string',
                        description=setting.description,
                        category=setting.category,
                        is_secret=setting.is_secret
                    )
                else:
                    # 创建新设置
                    SystemSetting.set(
                        key=db_key,
                        value=template_content,
                        value_type='string',
                        description=f'辅助生成提示词模板: {front_key}',
                        category='assistant_generation'
                    )

                current_app.logger.debug(f"已更新模板: {front_key}")

        # 提交数据库事务
        db.session.commit()

        current_app.logger.info("提示词模板更新成功")
        return jsonify({"success": True, "message": "提示词模板更新成功"})

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"更新提示词模板失败: {str(e)}")
        return jsonify({"error": f"更新提示词模板失败: {str(e)}"}), 500


@settings_bp.route('/settings/prompt-templates/reset', methods=['POST'])
def reset_prompt_templates():
    """重置提示词模板为默认值"""
    try:
        current_app.logger.debug("收到重置提示词模板请求")

        # 模板键名映射
        template_keys = {
            'roleSystemPrompt': 'prompt_template_roleSystemPrompt',
            'actionSpaceBackground': 'prompt_template_actionSpaceBackground',
            'actionSpaceRules': 'prompt_template_actionSpaceRules',
            'actionTaskDescription': 'prompt_template_actionTaskDescription',
            'userMessageExpand': 'prompt_template_userMessageExpand'
        }

        # 删除现有的自定义模板，让系统使用默认值
        for front_key, db_key in template_keys.items():
            setting = SystemSetting.query.filter_by(key=db_key).first()
            if setting:
                db.session.delete(setting)
                current_app.logger.debug(f"已删除自定义模板: {front_key}")

        # 提交数据库事务
        db.session.commit()

        current_app.logger.info("提示词模板已重置为默认值")
        return jsonify({
            "success": True,
            "message": "提示词模板已重置为默认值",
            "templates": DEFAULT_PROMPT_TEMPLATES
        })

    except Exception as e:
        db.session.rollback()
        current_app.logger.error(f"重置提示词模板失败: {str(e)}")
        return jsonify({"error": f"重置提示词模板失败: {str(e)}"}), 500